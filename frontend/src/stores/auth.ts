import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const user = ref<any | null>(null)

  const isAuthenticated = computed(() => !!token.value)

  async function login(email: string, password: string) {
    try {
      const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/login`, {
        email,
        password
      })

      token.value = response.data.token
      user.value = response.data.user

      localStorage.setItem('auth_token', token.value)

      // Set the default Authorization header for all future requests
      axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`

      return true
    } catch (error) {
      console.error('Login failed:', error)
      return false
    }
  }

  async function logout() {
    try {
      if (token.value) {
        await axios.post(`${import.meta.env.VITE_API_URL}/api/logout`, {}, {
          headers: {
            Authorization: `Bearer ${token.value}`
          }
        })
      }
    } catch (error) {
      console.error('Logout API call failed:', error)
    } finally {
      // Clear auth data regardless of API success
      token.value = null
      user.value = null
      localStorage.removeItem('auth_token')
      delete axios.defaults.headers.common['Authorization']
    }
  }

  async function fetchUser() {
    if (!token.value) return null

    try {
      const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/user`, {
        headers: {
          Authorization: `Bearer ${token.value}`
        }
      })

      user.value = response.data
      return user.value
    } catch (error) {
      console.error('Failed to fetch user:', error)
      // If unauthorized, clear token
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        token.value = null
        user.value = null
        localStorage.removeItem('auth_token')
      }
      return null
    }
  }

  // Initialize axios header if token exists
  if (token.value) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
  }

  return {
    token,
    user,
    isAuthenticated,
    login,
    logout,
    fetchUser
  }
})
