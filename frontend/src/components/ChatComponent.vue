<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import echo from '@/services/echo'
import { useAuthStore } from '@/stores/auth'

const messages = ref<string[]>([])
const newMessage = ref('')
const authStore = useAuthStore()

const sendMessage = async () => {
  if (!newMessage.value.trim()) return

  try {
    await fetch(`${import.meta.env.VITE_API_URL}/api/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ message: newMessage.value })
    })

    newMessage.value = ''
  } catch (error) {
    console.error('Failed to send message:', error)
  }
}

onMounted(() => {
  // Listen to private channel
  if (authStore.isAuthenticated) {
    echo.private('chat')
      .listen('MessageSent', (e: { message: string }) => {
        messages.value.push(e.message)
      })
  }
})

onUnmounted(() => {
  // Clean up listeners
  echo.leave('private-chat')
})
</script>

<template>
  <div class="chat-container">
    <h2>Real-time Chat</h2>

    <div v-if="!authStore.isAuthenticated" class="login-prompt">
      <p>Please log in to use the chat feature</p>
      <RouterLink to="/login" class="login-btn">Login</RouterLink>
    </div>

    <div v-else class="chat">
      <div class="messages">
        <div v-if="messages.length === 0" class="no-messages">
          No messages yet
        </div>
        <div v-for="(message, index) in messages" :key="index" class="message">
          {{ message }}
        </div>
      </div>

      <div class="message-input">
        <input
          v-model="newMessage"
          @keyup.enter="sendMessage"
          placeholder="Type a message..."
        />
        <button @click="sendMessage">Send</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  margin-bottom: 1rem;
  color: #333;
}

.login-prompt {
  text-align: center;
  padding: 2rem;
}

.login-btn {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #42b883;
  color: white;
  border-radius: 4px;
  text-decoration: none;
}

.messages {
  height: 300px;
  overflow-y: auto;
  padding: 1rem;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.no-messages {
  color: #999;
  text-align: center;
  padding: 2rem;
}

.message {
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.message-input {
  display: flex;
  gap: 0.5rem;
}

input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  padding: 0.5rem 1rem;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #3aa876;
}
</style>
