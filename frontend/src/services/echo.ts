import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

// Define window with Pusher property
declare global {
  interface Window {
    Pusher: typeof Pusher;
  }
}

// Make Pusher available globally
window.Pusher = Pusher;

// Initialize Laravel Echo
const echo = new Echo({
  broadcaster: 'pusher',
  key: import.meta.env.VITE_REVERB_APP_KEY || 'myappkey',
  wsHost: import.meta.env.VITE_REVERB_HOST || window.location.hostname,
  wsPort: parseInt(import.meta.env.VITE_REVERB_PORT || '8080'),
  wssPort: parseInt(import.meta.env.VITE_REVERB_PORT || '8080'),
  forceTLS: (import.meta.env.VITE_REVERB_SCHEME || 'http') === 'https',
  disableStats: true,
  enabledTransports: ['ws', 'wss'],
});

export default echo;
