<?php

namespace App\Http\Controllers;

use App\Events\MessageSent;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Send a new message.
     */
    public function send(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        // You might want to save the message to the database here

        // Broadcast the message
        broadcast(new MessageSent($request->message))->toOthers();

        return response()->json(['status' => 'Message sent successfully']);
    }
}
