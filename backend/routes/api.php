<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Broadcast;
use App\Http\Controllers\MessageController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Broadcasting authentication route
Broadcast::routes(['middleware' => ['auth:sanctum']]);

// Message routes
Route::middleware('auth:sanctum')->post('/messages', [MessageController::class, 'send']);
